'use client';

import dynamic from 'next/dynamic';
import { useState, useEffect } from 'react';
import { UniverReadyCallback, UniverInstance, UniverAPI } from '@/types/univer';

const UniverSheetOptimized = dynamic(
  () => import('./UniverSheetOptimized'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
        <div className="text-sm font-medium text-gray-700">正在加载表格组件...</div>
      </div>
    )
  }
);

interface UniverWrapperProps {
  onReady?: UniverReadyCallback;
  initialData?: Record<string, unknown>;
}

export default function UniverWrapperOptimized({ onReady, initialData }: UniverWrapperProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingStage, setLoadingStage] = useState('正在初始化...');

  const handleReady = (instance: UniverInstance, api: UniverAPI) => {
    // 核心功能就绪，立即可用
    setLoadingStage('✓ 表格已就绪');
    
    // 很短的延迟后隐藏加载状态
    setTimeout(() => {
      setIsLoading(false);
      if (onReady) {
        onReady(instance, api);
      }
    }, 50);
  };

  // 简化的加载状态更新
  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        setLoadingStage('正在准备工作区...');
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  return (
    <div className="relative h-full">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-start bg-white bg-opacity-90 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3 mt-16"></div>
          <div className="text-base font-bold text-gray-900 mb-1">表格加载中，马上就好……</div>
          <div className="text-sm text-gray-600 font-medium">{loadingStage}</div>
        </div>
      )}
      <UniverSheetOptimized onReady={handleReady} initialData={initialData} />
    </div>
  );
}
