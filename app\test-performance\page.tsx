'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';

// 原版组件
const UniverWrapper = dynamic(() => import('@/app/components/UniverWrapper'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
      <div className="text-gray-600">加载原版组件中...</div>
    </div>
  )
});

// 优化版组件
const UniverWrapperOptimized = dynamic(() => import('@/app/components/UniverWrapperOptimized'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
      <div className="text-gray-600">加载优化版组件中...</div>
    </div>
  )
});

export default function TestPerformancePage() {
  const [activeTab, setActiveTab] = useState<'original' | 'optimized'>('original');
  const [loadTimes, setLoadTimes] = useState<{
    original?: number;
    optimized?: number;
  }>({});

  const handleReady = (type: 'original' | 'optimized') => {
    const endTime = performance.now();
    const startTime = (window as any)[`${type}StartTime`];
    if (startTime) {
      const loadTime = endTime - startTime;
      setLoadTimes(prev => ({
        ...prev,
        [type]: loadTime
      }));
      console.log(`${type} 加载时间:`, loadTime, 'ms');
    }
  };

  const startTimer = (type: 'original' | 'optimized') => {
    (window as any)[`${type}StartTime`] = performance.now();
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">UniverSheet 性能测试</h1>
          <p className="text-gray-600 mb-6">
            比较原版和优化版的加载性能。优化版采用真正的懒加载策略，核心功能优先加载，扩展功能后台加载。
          </p>
          
          {/* 性能统计 */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">加载时间统计</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium text-blue-900">原版组件</h3>
                <p className="text-2xl font-bold text-blue-600">
                  {loadTimes.original ? `${loadTimes.original.toFixed(0)}ms` : '未测试'}
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-medium text-green-900">优化版组件</h3>
                <p className="text-2xl font-bold text-green-600">
                  {loadTimes.optimized ? `${loadTimes.optimized.toFixed(0)}ms` : '未测试'}
                </p>
              </div>
            </div>
            {loadTimes.original && loadTimes.optimized && (
              <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
                <p className="text-yellow-800">
                  性能提升: {((loadTimes.original - loadTimes.optimized) / loadTimes.original * 100).toFixed(1)}%
                  {loadTimes.optimized < loadTimes.original ? ' 🎉' : ' ⚠️'}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* 标签页 */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => {
                  setActiveTab('optimized');
                  setTimeout(() => startTimer('optimized'), 100);
                }}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'optimized'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                优化版 (推荐)
              </button>
              <button
                onClick={() => {
                  setActiveTab('original');
                  setTimeout(() => startTimer('original'), 100);
                }}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'original'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                原版
              </button>
            </nav>
          </div>

          <div className="p-6">
            <div className="h-96">
              {activeTab === 'optimized' && (
                <UniverWrapperOptimized
                  onReady={() => handleReady('optimized')}
                  initialData={{
                    'A1': '测试数据',
                    'B1': 123,
                    'C1': '优化版本'
                  }}
                />
              )}
              {activeTab === 'original' && (
                <UniverWrapper
                  onReady={() => handleReady('original')}
                  initialData={{
                    'A1': '测试数据',
                    'B1': 123,
                    'C1': '原版本'
                  }}
                />
              )}
            </div>
          </div>
        </div>

        {/* 优化说明 */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">优化策略说明</h2>
          <div className="space-y-4 text-gray-600">
            <div>
              <h3 className="font-medium text-gray-900">1. 菜单功能完整</h3>
              <p>基础功能（公式、数据验证、筛选、排序等）立即加载，确保菜单完整可用</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">2. 分层加载策略</h3>
              <p>核心+基础功能立即加载，高级功能500ms后加载，专业功能2秒后加载</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">3. 真正的代码分割</h3>
              <p>高级功能（条件格式、表格）和专业功能（图表、数据透视表）实现真正懒加载</p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">4. 用户体验优化</h3>
              <p>核心功能就绪后立即可用，不等待所有功能加载完成</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
