'use client';

import dynamic from 'next/dynamic';
import { useState, useEffect } from 'react';
import { UniverReadyCallback, UniverInstance, UniverAPI } from '@/types/univer';

const UniverSheet = dynamic(
  () => import('./UniverSheet'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        {/* <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div> */}
        {/* <div className="text-lg font-medium text-gray-700 mb-2">表格加载中，马上就好……</div> */}
        {/* <div className="text-sm text-gray-500">正在初始化Excel组件</div> */}
      </div>
    )
  }
);

interface UniverWrapperProps {
  onReady?: UniverReadyCallback;
  initialData?: Record<string, unknown>;
}

export default function UniverWrapper({ onReady, initialData }: UniverWrapperProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingStage, setLoadingStage] = useState('初始化中...');
  const [coreReady, setCoreReady] = useState(false);

  const handleReady = (instance: UniverInstance, api: UniverAPI) => {
    // 核心组件准备就绪，立即显示表格
    setCoreReady(true);
    setLoadingStage('核心功能已就绪');

    // 短暂延迟后隐藏加载状态，让用户可以开始使用
    setTimeout(() => {
      setIsLoading(false);
      if (onReady) {
        onReady(instance, api);
      }
    }, 100); // 减少延迟时间
  };

  // 优化的加载阶段更新
  useEffect(() => {
    if (!coreReady) {
      const stages = [
        { text: '正在加载核心组件...', delay: 100 },
        { text: '正在初始化表格...', delay: 500 },
        { text: '即将完成...', delay: 800 },
      ];

      const timers: NodeJS.Timeout[] = [];

      stages.forEach(({ text, delay }) => {
        const timer = setTimeout(() => {
          if (isLoading && !coreReady) {
            setLoadingStage(text);
          }
        }, delay);
        timers.push(timer);
      });

      // 清理定时器
      return () => {
        timers.forEach(timer => clearTimeout(timer));
      };
    }
  }, [isLoading, coreReady]);

  return (
    <div className="relative h-full">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-start bg-white bg-opacity-95 rounded-lg backdrop-blur-sm">
          <div className="animate-spin rounded-full h-10 w-10 border-b-3 border-blue-600 mb-4 mt-16"></div>
          <div className="text-lg font-bold text-gray-900 mb-2">表格加载中，马上就好……</div>
          <div className="text-sm text-gray-700 animate-pulse font-medium">{loadingStage}</div>
          {coreReady && (
            <div className="text-xs text-green-600 mt-2 animate-pulse">
              ✓ 基础功能已可用，高级功能正在后台加载...
            </div>
          )}
        </div>
      )}
      <UniverSheet onReady={handleReady} initialData={initialData} />
    </div>
  );
}