/**
 * 真正的懒加载插件配置
 * 使用动态导入实现真正的代码分割
 */

import type { Plugin, PluginCtor } from '@univerjs/core';

// 高级功能插件的懒加载函数（条件格式、表格等）
export async function getLazyPlugins(): Promise<Array<[PluginCtor<Plugin>] | [PluginCtor<Plugin>, unknown]>> {
    const [
        { UniverSheetsConditionalFormattingPlugin },
        { UniverSheetsConditionalFormattingUIPlugin },
        { UniverSheetsTablePlugin },
        { UniverSheetsTableUIPlugin },
    ] = await Promise.all([
        import('@univerjs/sheets-conditional-formatting'),
        import('@univerjs/sheets-conditional-formatting-ui'),
        import('@univerjs/sheets-table'),
        import('@univerjs/sheets-table-ui'),
    ]);

    return [
        [UniverSheetsConditionalFormattingPlugin],
        [UniverSheetsConditionalFormattingUIPlugin],
        [UniverSheetsTablePlugin],
        [UniverSheetsTableUIPlugin],
    ];
}

// 高级功能插件的懒加载函数
export async function getVeryLazyPlugins(): Promise<Array<[PluginCtor<Plugin>] | [PluginCtor<Plugin>, unknown]>> {
    const [
        { UniverSheetsChartPlugin },
        { UniverSheetsChartUIPlugin },
        { UniverSheetsPivotTablePlugin },
        { UniverSheetsPivotTableUIPlugin },
    ] = await Promise.all([
        import('@univerjs-pro/sheets-chart'),
        import('@univerjs-pro/sheets-chart-ui'),
        import('@univerjs-pro/sheets-pivot'),
        import('@univerjs-pro/sheets-pivot-ui'),
    ]);

    return [
        [UniverSheetsChartPlugin],
        [UniverSheetsChartUIPlugin],
        [UniverSheetsPivotTablePlugin],
        [UniverSheetsPivotTableUIPlugin],
    ];
}

// 懒加载高级功能语言包
export async function getLazyLocales() {
    const [
        SheetsConditionalFormattingUIZhCN,
        SheetsTableUIZhCN,
    ] = await Promise.all([
        import('@univerjs/sheets-conditional-formatting-ui/locale/zh-CN'),
        import('@univerjs/sheets-table-ui/locale/zh-CN'),
    ]);

    return {
        SheetsConditionalFormattingUIZhCN: SheetsConditionalFormattingUIZhCN.default,
        SheetsTableUIZhCN: SheetsTableUIZhCN.default,
    };
}

// 懒加载专业功能语言包
export async function getVeryLazyLocales() {
    const [
        SheetsPivotTableUIZhCN,
        SheetsPivotTableZhCN,
        SheetsChartUIZhCN,
        SheetsChartZhCN,
    ] = await Promise.all([
        import('@univerjs-pro/sheets-pivot-ui/locale/zh-CN'),
        import('@univerjs-pro/sheets-pivot/locale/zh-CN'),
        import('@univerjs-pro/sheets-chart-ui/locale/zh-CN'),
        import('@univerjs-pro/sheets-chart/locale/zh-CN'),
    ]);

    return {
        SheetsPivotTableUIZhCN: SheetsPivotTableUIZhCN.default,
        SheetsPivotTableZhCN: SheetsPivotTableZhCN.default,
        SheetsChartUIZhCN: SheetsChartUIZhCN.default,
        SheetsChartZhCN: SheetsChartZhCN.default,
    };
}
